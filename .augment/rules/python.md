---
type: "manual"
---

** General Guidelines **

- write code in idiomatic python code with well-known python design patterns and object-oriented programming approaches
- avoid repeating code; reuse existing functionality when possible
- assume the user has python version 3.12 installed
- write concise code that can easily be tested
- use type hints
- prefer to use F-string for formatting strings
- use logging: replace print statements with logging for better control over output

** Libraries **

- use pydantic for data validation and settings management
- use the langgraph framework and asyncio
- for web frontends use flask

** User Interaction **

- create short code segments implementing only the users request
- before including another library or dependency outiside the python standard library consult the user
